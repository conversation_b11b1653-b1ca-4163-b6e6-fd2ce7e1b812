from typing import List

from fastapi import APIRouter, HTTPException
from sqlmodel import select

from src.auth.auth import CurrentUser, FlexibleUser
from src.db.session import DBSession
from src.patients.models import Patient, PatientCreate, PatientRead, PatientUpdate

patient_router = APIRouter(
    prefix="/patients",
    tags=["patients"],
    responses={404: {"description": "Not found"}},
)


# Create a new patient for the current user
@patient_router.post("/", response_model=PatientRead)
async def create_patient(
    *, session: DBSession, current_user: FlexibleUser, patient_in: PatientCreate
):
    # The id_num is globally unique in the DB, so we can just check existence
    # existing_patient_q = await session.exec(
    #     select(Patient).where(Patient.id_num == patient_in.id_num)
    # )
    # if existing_patient_q.one_or_none():
    #     raise HTTPException(
    #         status_code=400, detail="Patient with this ID number already exists."
    #     )

    patient = Patient.model_validate(patient_in, update={"user_id": current_user.id})
    session.add(patient)
    await session.commit()
    await session.refresh(patient)
    return patient


# List all patients for the current user
@patient_router.get("/", response_model=List[PatientRead])
async def read_patients(*, session: DBSession, current_user: CurrentUser):
    patients_q = await session.exec(
        select(Patient).where(Patient.user_id == current_user.id)
    )
    return patients_q.all()


# Get a specific patient by ID
@patient_router.get("/{patient_id}", response_model=PatientRead)
async def read_patient(
    *, session: DBSession, current_user: CurrentUser, patient_id: int
):
    patient = await session.get(Patient, patient_id)
    if not patient:
        raise HTTPException(status_code=404, detail="Patient not found")
    if patient.user_id != current_user.id:
        raise HTTPException(
            status_code=403, detail="Not authorized to access this patient"
        )
    return patient


# Update a patient
@patient_router.put("/{patient_id}", response_model=PatientRead)
async def update_patient(
    *,
    session: DBSession,
    current_user: CurrentUser,
    patient_id: int,
    patient_in: PatientUpdate,
):
    patient = await session.get(Patient, patient_id)
    if not patient:
        raise HTTPException(status_code=404, detail="Patient not found")
    if patient.user_id != current_user.id:
        raise HTTPException(
            status_code=403, detail="Not authorized to update this patient"
        )

    patient_data = patient_in.model_dump(exclude_unset=True)
    for key, value in patient_data.items():
        setattr(patient, key, value)

    session.add(patient)
    await session.commit()
    await session.refresh(patient)
    return patient


# Delete a patient
@patient_router.delete("/{patient_id}", status_code=204)
async def delete_patient(*, session: DBSession, current_user: CurrentUser, patient_id: int):
    patient = await session.get(Patient, patient_id)
    if not patient or patient.user_id != current_user.id:
        # Do not let the user know if a patient exists or not for another user
        raise HTTPException(status_code=404, detail="Patient not found")
    
    await session.delete(patient)
    await session.commit()
    return

