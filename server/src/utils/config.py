from pydantic_settings import BaseSettings
from typing import Dict, Optional

class Settings(BaseSettings):

    PATIENT_APP_ID: str
    PATIENT_APP_SECRET: str

    DOCTOR_APP_ID: str
    DOCTOR_APP_SECRET: str

    @property
    def WX_CREDENTIALS(self) -> Dict[str, Dict[str, str]]:
        return {
            "patient": {
                "id": self.PATIENT_APP_ID,
                "secret": self.PATIENT_APP_SECRET,
            },
            "doctor": {
                "id": self.DOCTOR_APP_ID,
                "secret": self.DOCTOR_APP_SECRET,
            },
        }

    def get_wx_creds_by_identifier(self, identifier: str) -> Optional[Dict[str, str]]:
        return self.WX_CREDENTIALS.get(identifier)

    JWT_SECRET: str = "your-should-set-secret-key-in-env"
    JWT_ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: float = 600

    PAY_MCHID: Optional[str] = None
    PAY_API_V3_KEY: Optional[str] = None
    PAY_CERT_SERIAL_NO: Optional[str] = None
    PAY_PRIVATE_KEY_PATH: Optional[str] = None
    PAY_NOTIFY_URL: Optional[str] = None

    USE_MOCK_PAYMENT: bool = True

    class Config:
        case_sensitive = False

settings = Settings()
