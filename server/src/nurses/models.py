from typing import Optional
from sqlmodel import SQLModel, Field, Relationship
import enum

from src.users.models import User, UserRead


class NurseGender(enum.StrEnum):
    MALE = "男"
    FEMALE = "女"


class NurseBase(SQLModel):
    name: str = Field(index=True, max_length=50)
    gender: NurseGender = Field()
    title: str = Field(max_length=100)  # 职称
    department: str = Field(max_length=100)  # 科室
    license_number: Optional[str] = Field(default=None, max_length=50)  # 护士执业证号
    years_of_experience: Optional[int] = Field(default=0)  # 工作年限
    specialties: Optional[str] = Field(default=None, max_length=200)  # 专业特长
    is_active: bool = Field(default=True)


class Nurse(NurseBase, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    # 1:1 relationship with User
    user_id: int = Field(foreign_key="user.id", unique=True)
    # Relationship back to User
    user: User = Relationship(back_populates="nurse")


class NurseCreate(NurseBase):
    # When creating a nurse, we also need User info
    openid: str = Field(min_length=1)
    session_key: str = Field(min_length=1)
    nickname: Optional[str] = None
    avatar: Optional[str] = None
    phone: Optional[str] = Field(default=None, max_length=20)


class NurseRead(NurseBase):
    id: int
    user_id: int
    # Include basic user info in read response
    user_openid: Optional[str] = None
    user_nickname: Optional[str] = None
    user_phone: Optional[str] = None
    user_avatar: Optional[str] = None


class NurseUpdate(SQLModel):
    name: Optional[str] = Field(default=None, max_length=50)
    gender: Optional[NurseGender] = None
    title: Optional[str] = Field(default=None, max_length=100)
    department: Optional[str] = Field(default=None, max_length=100)
    license_number: Optional[str] = Field(default=None, max_length=50)
    years_of_experience: Optional[int] = None
    specialties: Optional[str] = Field(default=None, max_length=200)
    is_active: Optional[bool] = None


class NurseWithUser(NurseRead):
    user: UserRead
