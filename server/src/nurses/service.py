from typing import Optional
from sqlmodel import Session, select
from src.users.models import User, UserCreate
from src.nurses.models import Nurse, Nurse<PERSON><PERSON>, <PERSON>Up<PERSON>, NurseWithUser
from src.db.session import DBSession


class NurseService:
    def __init__(self, session: DBSession):
        self.session = session

    async def create_nurse_with_user(self, nurse_data: NurseCreate) -> NurseWithUser:
        """
        Creates a nurse along with their associated user account
        If user already exists, creates nurse profile for them
        """
        # Check if user already exists
        statement = select(User).where(User.openid == nurse_data.openid)
        existing_user = (await self.session.exec(statement)).first()
        
        if existing_user:
            user = existing_user
            # Update user info if provided
            if nurse_data.nickname:
                user.nickname = nurse_data.nickname
            if nurse_data.avatar:
                user.avatar = nurse_data.avatar
            if nurse_data.phone:
                user.phone = nurse_data.phone
            self.session.add(user)
            await self.session.commit()
            await self.session.refresh(user)
        else:
            # Create new user
            user_data = UserCreate(
                openid=nurse_data.openid,
                session_key=nurse_data.session_key,
                nickname=nurse_data.nickname,
                avatar=nurse_data.avatar,
                phone=nurse_data.phone
            )
            
            user = User.from_orm(user_data)
            self.session.add(user)
            await self.session.commit()
            await self.session.refresh(user)
        
        # Check if nurse profile already exists
        existing_nurse_statement = select(Nurse).where(Nurse.user_id == user.id)
        existing_nurse = (await self.session.exec(existing_nurse_statement)).first()
        
        if existing_nurse:
            raise ValueError(f"Nurse profile already exists for user {user.openid}")
        
        # Create the Nurse profile linked to the user
        nurse_profile_data = {
            "name": nurse_data.name,
            "gender": nurse_data.gender,
            "title": nurse_data.title,
            "department": nurse_data.department,
            "license_number": nurse_data.license_number,
            "years_of_experience": nurse_data.years_of_experience,
            "specialties": nurse_data.specialties,
            "is_active": nurse_data.is_active,
            "user_id": user.id
        }
        
        nurse = Nurse(**nurse_profile_data)
        self.session.add(nurse)
        await self.session.commit()
        await self.session.refresh(nurse)
        
        # Load the complete nurse with user relationship
        return await self.get_nurse_with_user(nurse.id)
    
    async def get_nurse_with_user(self, nurse_id: int) -> Optional[NurseWithUser]:
        """Get nurse with full user details"""
        statement = (
            select(Nurse)
            .where(Nurse.id == nurse_id)
        )
        nurse = (await self.session.exec(statement)).first()
        
        if not nurse:
            return None
        
        # Load user relationship
        user = await self.session.get(User, nurse.user_id)
        
        # Construct response with user details
        nurse_with_user = NurseWithUser(
            **nurse.dict(),
            user_openid=user.openid,
            user_nickname=user.nickname,
            user_phone=user.phone,
            user_avatar=user.avatar,
            user=user
        )
        
        return nurse_with_user
    
    async def get_nurses_by_department(self, department: str) -> list[NurseWithUser]:
        """Get all active nurses in a specific department"""
        statement = (
            select(Nurse)
            .join(User)
            .where(
                Nurse.department == department,
                Nurse.is_active == True,
                User.is_active == True
            )
        )
            
        nurses = (await self.session.exec(statement)).all()
        return [
            await self.get_nurse_with_user(nurse.id)
            for nurse in nurses
        ]

    async def update_nurse(self, nurse_id: int, nurse_update: NurseUpdate) -> Optional[NurseWithUser]:
        """Update nurse profile"""
        nurse = await self.session.get(Nurse, nurse_id)
        if not nurse:
            return None
        
        # Update only provided fields
        update_data = nurse_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(nurse, field, value)
        
        self.session.add(nurse)
        await self.session.commit()
        await self.session.refresh(nurse)
        
        return await self.get_nurse_with_user(nurse_id)
    
    async def deactivate_nurse(self, nurse_id: int) -> bool:
        nurse = await self.session.get(Nurse, nurse_id)
        if not nurse:
            return False
        
        # Deactivate nurse profile
        nurse.is_active = False
        
        self.session.add(nurse)
        await self.session.commit()
        
        return True
    
    async def get_nurses(
        self, 
        skip: int = 0, 
        limit: int = 100,
        department: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> list[NurseWithUser]:
        """Get list of nurses with optional filtering"""
        statement = (
            select(Nurse)
            .join(User)
            .offset(skip)
            .limit(limit)
        )
        
        # Add filters
        if department:
            statement = statement.where(Nurse.department == department)
        if is_active is not None:
            statement = statement.where(Nurse.is_active == is_active)
        
        nurses = (await self.session.exec(statement)).all()
        return [
            await self.get_nurse_with_user(nurse.id)
            for nurse in nurses
        ]

    async def search_nurses(self, query: str) -> list[NurseWithUser]:
        """Search nurses by name or employee ID"""
        statement = (
            select(Nurse)
            .join(User)
            .where(
                (Nurse.name.ilike(f"%{query}%")) |
                (Nurse.license_number.ilike(f"%{query}%"))
            )
        )
        
        nurses = (await self.session.exec(statement)).all()
        return [
            await self.get_nurse_with_user(nurse.id)
            for nurse in nurses
        ]

    async def get_nurse(self, nurse_id: int) -> Optional[NurseWithUser]:
        """Get a specific nurse by ID"""
        return await self.get_nurse_with_user(nurse_id)

    async def delete_nurse(self, nurse_id: int) -> bool:
        """Soft delete a nurse"""
        nurse = await self.session.get(Nurse, nurse_id)
        if not nurse:
            return False
        
        nurse.is_active = False
        self.session.add(nurse)
        await self.session.commit()
        return True

    async def get_available_nurses(
        self,
        department: Optional[str] = None
    ) -> list[NurseWithUser]:
        """Get available nurses for assignment"""
        statement = (
            select(Nurse)
            .join(User)
            .where(
                Nurse.is_active == True,
                User.is_active == True
            )
        )

        if department:
            statement = statement.where(Nurse.department == department)

        nurses = (await self.session.exec(statement)).all()
        return [
            await self.get_nurse_with_user(nurse.id)
            for nurse in nurses
        ]

    async def get_nurse_by_user_id(self, user_id: int) -> Optional[NurseWithUser]:
        """Get nurse profile by user ID"""
        statement = select(Nurse).where(Nurse.user_id == user_id)
        nurse = (await self.session.exec(statement)).first()

        if not nurse:
            return None

        return await self.get_nurse_with_user(nurse.id)

    async def create_nurse_profile_for_user(self, user_id: int, nurse_data: NurseCreate) -> NurseWithUser:
        """Create nurse profile for an existing user"""
        # Check if nurse profile already exists
        existing_nurse_statement = select(Nurse).where(Nurse.user_id == user_id)
        existing_nurse = (await self.session.exec(existing_nurse_statement)).first()

        if existing_nurse:
            raise ValueError(f"Nurse profile already exists for user {user_id}")

        # Create the Nurse profile linked to the user
        nurse_profile_data = {
            "name": nurse_data.name,
            "gender": nurse_data.gender,
            "title": nurse_data.title,
            "department": nurse_data.department,
            "license_number": nurse_data.license_number,
            "years_of_experience": nurse_data.years_of_experience,
            "specialties": nurse_data.specialties,
            "is_active": nurse_data.is_active,
            "user_id": user_id
        }

        nurse = Nurse(**nurse_profile_data)
        self.session.add(nurse)
        await self.session.commit()
        await self.session.refresh(nurse)

        # Update user info if provided
        user = await self.session.get(User, user_id)
        if user:
            if nurse_data.nickname:
                user.nickname = nurse_data.nickname
            if nurse_data.avatar:
                user.avatar = nurse_data.avatar
            if nurse_data.phone:
                user.phone = nurse_data.phone
            self.session.add(user)
            await self.session.commit()

        return await self.get_nurse_with_user(nurse.id)