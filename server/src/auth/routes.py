
from typing import Union
import httpx
from fastapi import APIRouter, HTTPException
from sqlmodel import select

from src.utils.config import settings
from src.db.session import DBSession
from src.auth.auth import CurrentUser, create_access_token, authenticate_user
from src.auth.models import Token, WxLogin, PwdLogin
# need import to ensure relationship works for db session
from src.patients.models import Patient
from src.orders.models import Order
from src.users.models import User

import logging


auth_router = APIRouter(
    prefix="/auth",
    tags=["auth"],
    responses={404: {"description": "Not found"}},
)

@auth_router.post("/login", response_model=Token)
async def login(data: Union[WxLogin, PwdLogin], session: DBSession):
    if isinstance(data, PwdLogin):
        # Admin panel username/password login
        user = await authenticate_user(session, data.username, data.password)
        if not user or not user.is_admin:
            raise HTTPException(
                status_code=401,
                detail="Incorrect username or password, or user is not an admin.",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        token = create_access_token(uid=str(user.id))
        return {"token": token, "user_id": user.id, "is_admin": user.is_admin, "user": user.model_dump()}

    elif isinstance(data, WxLogin):
        # WeChat mini-program login
        logging.info(f"Received WeChat login request with code: {data.code} for app: {data.app_key}")
        
        creds = settings.get_wx_creds_by_identifier(data.app_key)
        if not creds:
            raise HTTPException(status_code=400, detail=f"Invalid app_key: {data.app_key}")

        app_id = creds["id"]
        app_secret = creds["secret"]
        code = data.code
        wx_url = f"https://api.weixin.qq.com/sns/jscode2session?appid={app_id}&secret={app_secret}&js_code={code}&grant_type=authorization_code"
        
        async with httpx.AsyncClient() as client:
            resp = await client.get(wx_url)
            wx_data = resp.json()
        
        logging.info(f"Received WeChat login response: {wx_data}")
        
        if "errcode" in wx_data:
            raise HTTPException(401, f"微信认证失败: {wx_data.get('errmsg')}")
        
        openid = wx_data["openid"]
        session_key = wx_data["session_key"]

        logging.info(f"Received WeChat login for app {data.app_key} with openid: {openid}, key: {session_key}")

        result = await session.exec(select(User).where(User.openid == openid))
        user = result.one_or_none()
        if not user:
            user = User(openid=openid, session_key=session_key)
            session.add(user)
        else:
            user.session_key = session_key
        
        await session.commit()
        await session.refresh(user)

        token = create_access_token(uid=str(user.id), openid=openid)
        return {
            "token": token,
            "user_id": user.id,
            "is_admin": user.is_admin,
            "user": user.model_dump()
        }
    raise HTTPException(status_code=400, detail="Invalid login request payload")


@auth_router.get("/me", response_model=User)
async def read_users_me(current_user: CurrentUser):
    return current_user
