<template>
  <view class="login-container">
    <view class="login-header">
      <image class="logo" src="/static/logo.png" mode="aspectFit"></image>
      <text class="app-name">西安市第五医院医护端</text>
    </view>

    <view class="login-content">
      <view class="user-info" v-if="userInfo != null">
        <image class="avatar" :src="userInfo.avatarUrl" mode="aspectFill"></image>
        <text class="nickname">{{ userInfo.nickname }}</text>
        <text class="welcome">欢迎回来！</text>
      </view>

      <view class="login-actions">
        <button v-if="!isLoggedIn" class="login-btn" @click="handleWeChatLogin" :loading="isLogging">
          {{ isLogging ? '登录中...' : '微信登录' }}
        </button>

        <button v-else class="logout-btn" @click="handleLogout">
          退出登录
        </button>
      </view>

      <view class="tips" v-if="!isLoggedIn">
        <text>点击登录即表示同意用户协议和隐私政策</text>
      </view>
    </view>
  </view>
</template>

<script lang="uts">
  import { API_BASE_URL, APP_NAME, LOGIN_DESC } from '@/utils/config.uts'
  import { UserInfo, LoginResponse, isUserLoggedIn, getCurrentUser, getCurrentToken, isCurrentUserAdmin, clearAuthData, saveAuthData } from '@/utils/auth.uts'

  export default {
    data() {
      return {
        isLoggedIn: false as boolean,
        isLogging: false as boolean,
        userInfo: null as UserInfo | null,
        isAdmin: false as boolean
      }
    },
    
    onLoad() {
      this.checkLoginStatus()
    },
    
    methods: {
      // 检查登录状态
      checkLoginStatus() {
        if (isUserLoggedIn()) {
          const token = getCurrentToken()
          const cachedUserInfo = getCurrentUser()
          const cachedIsAdmin = isCurrentUserAdmin()

          this.isLoggedIn = true
          this.userInfo = cachedUserInfo
          this.isAdmin = cachedIsAdmin

          // Validate token before navigating
          this.validateToken(token).then((isValid: boolean) => {
            if (isValid) {
              setTimeout(() => {
                uni.navigateTo({
                  url: '/pages/index/index',
                });
              }, 1000);
            } else {
              // Token is invalid, force user to log in again
              clearAuthData()

              // 重置状态
              this.isLoggedIn = false
              this.userInfo = null
            }
          });
        }
      },

      // 微信登录
      async handleWeChatLogin() {
        try {
          this.isLogging = true

          // 1. 先获取用户信息授权（必须在用户点击事件中同步调用）
          const userProfile = await this.getUserProfile()

          // 2. 获取微信登录凭证
          const loginRes = await this.getWeChatLoginCode()

          // 3. 发送到后端进行登录验证
          const loginResult = await this.loginToServer(loginRes.code)

          if (loginResult.token != '') {
            // The user info from our database. We assume the login endpoint returns the user object.
            // This ensures that any nickname/avatar set in the profile is not overwritten on re-login.
            const dbUser = loginResult.user || {} as UserInfo;

            // Create a consistent user info object for storage, prioritizing DB data over WeChat's.
            const storedUserInfo: UserInfo = {
              nickname: dbUser.nickname || userProfile.nickName,
              avatarUrl: dbUser.avatarUrl || userProfile.avatarUrl,
            };

            // Save to storage using utility function
            saveAuthData(loginResult.token, storedUserInfo, loginResult.is_admin);

            this.userInfo = storedUserInfo;
            this.isLoggedIn = true
            this.isAdmin = loginResult.is_admin

            uni.showToast({
              title: '登录成功',
              icon: 'success'
            })

            setTimeout(() => {
              uni.navigateTo({
                url: '/pages/index/index',
              })
            }, 1000)
          } else {
            throw new Error('登录失败，未获取到token')
          }

        } catch (error: any) {
          console.error('登录失败:', error)
          uni.showToast({
            title: error.message || '登录失败',
            icon: 'none'
          })
        } finally {
          this.isLogging = false
        }
      },

      // 获取用户信息
      getUserProfile(): Promise<any> {
        return new Promise((resolve, reject) => {
          uni.getUserProfile({
            desc: LOGIN_DESC,
            success: (res) => {
              resolve(res.userInfo)
            },
            fail: (error) => {
              console.error('获取用户信息失败:', error)
              reject(new Error('获取用户信息失败'))
            }
          })
        })
      },

      // 获取微信登录凭证
      getWeChatLoginCode(): Promise<any> {
        return new Promise((resolve, reject) => {
          uni.login({
            provider: 'weixin',
            success: resolve,
            fail: reject
          })
        })
      },

      // 发送登录信息到服务器
      async loginToServer(code: string): Promise<LoginResponse> {
        try {
          const response = await new Promise<any>((resolve, reject) => {
            uni.request({
              url: `${API_BASE_URL}/auth/login`,
              method: 'POST',
              data: {
                code: code
              },
              header: {
                'Content-Type': 'application/json'
              },
              success: (res) => {
                if (res.statusCode == 200) {
                  resolve(res.data)
                } else {
                  reject(new Error(`请求失败: ${res.statusCode}`))
                }
              },
              fail: reject
            })
          })

          return response as LoginResponse
        } catch (error: any) {
          console.error('服务器登录失败:', error)
          throw error
        }
      },

      // 退出登录
      handleLogout() {
        uni.showModal({
          title: '提示',
          content: '确定要退出登录吗？',
          success: (res) => {
            if (res.confirm) {
              // 清除本地存储
              clearAuthData()

              // 重置状态
              this.isLoggedIn = false
              this.userInfo = null

              uni.showToast({
                title: '已退出登录',
                icon: 'success'
              })
            }
          }
        })
      },

      validateToken(token: string): Promise<boolean> {
        return new Promise((resolve) => {
          uni.request({
            url: `${API_BASE_URL}/auth/me`,
            method: 'GET',
            header: {
              'Authorization': `Bearer ${token}`
            },
            success: (res) => {
              resolve(res.statusCode == 200);
            },
            fail: () => {
              resolve(false);
            }
          });
        });
      }
    }
  }
</script>

<style lang="scss" scoped>
  .login-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    flex-direction: column;
    padding: 0 60rpx;
  }

  .login-header {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding-top: 200rpx;

    .logo {
      width: 160rpx;
      height: 160rpx;
      border-radius: 20rpx;
      margin-bottom: 40rpx;
    }

    .app-name {
      font-size: 48rpx;
      font-weight: bold;
      color: #ffffff;
      text-align: center;
    }
  }

  .login-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    padding-top: 100rpx;
    padding-bottom: 100rpx;
  }

  .user-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 80rpx;

    .avatar {
      width: 120rpx;
      height: 120rpx;
      border-radius: 60rpx;
      margin-bottom: 20rpx;
    }

    .nickname {
      font-size: 36rpx;
      font-weight: bold;
      color: #ffffff;
      margin-bottom: 10rpx;
    }

    .welcome {
      font-size: 28rpx;
      color: rgba(255, 255, 255, 0.8);
    }
  }

  .login-actions {
    margin-bottom: 60rpx;

    .login-btn,
    .logout-btn {
      width: 100%;
      height: 96rpx;
      background: #ffffff;
      color: #667eea;
      border: none;
      border-radius: 48rpx;
      font-size: 36rpx;
      font-weight: bold;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
    }

    .login-btn:active,
    .logout-btn:active {
      transform: scale(0.98);
      opacity: 0.9;
    }

    .logout-btn {
      background: rgba(255, 255, 255, 0.2);
      color: #ffffff;
      border: 2rpx solid rgba(255, 255, 255, 0.3);
    }
  }

  .tips {
    text-align: center;

    text {
      font-size: 24rpx;
      color: rgba(255, 255, 255, 0.7);
      line-height: 1.5;
    }
  }
</style>
