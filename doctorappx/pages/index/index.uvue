<template>
	<view class="container">
		<image class="logo" src="/static/logo.png"></image>
		<text class="title">{{title}}</text>
		<view class="actions">
			<button class="btn" @click="goToLogin">前往登录</button>
			<button class="btn" @click="checkAuth">检查登录状态</button>
		</view>
		<view class="user-info" v-if="userInfo != null">
			<text class="info-text">当前用户: {{userInfo.nickname}}</text>
			<text class="info-text">管理员: {{isAdmin ? '是' : '否'}}</text>
		</view>
	</view>
</template>

<script lang="uts">
	import { isUserLoggedIn, getCurrentUser, isCurrentUserAdmin } from '@/utils/auth.uts'

	type UserInfo = {
		nickname: string
		avatarUrl: string
	}

	export default {
		data() {
			return {
				title: '西安市第五医院医护端',
				userInfo: null as UserInfo | null,
				isAdmin: false as boolean
			}
		},
		onLoad() {
			this.checkLoginStatus()
		},
		onShow() {
			this.checkLoginStatus()
		},
		methods: {
			checkLoginStatus() {
				if (isUserLoggedIn()) {
					this.userInfo = getCurrentUser()
					this.isAdmin = isCurrentUserAdmin()
				} else {
					this.userInfo = null
					this.isAdmin = false
				}
			},

			goToLogin() {
				uni.navigateTo({
					url: '/pages/login/login'
				})
			},

			checkAuth() {
				if (isUserLoggedIn()) {
					uni.showToast({
						title: '已登录',
						icon: 'success'
					})
				} else {
					uni.showToast({
						title: '未登录',
						icon: 'none'
					})
				}
			}
		}
	}
</script>

<style>
	.container {
		padding: 40rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.logo {
		height: 200rpx;
		width: 200rpx;
		margin: 100rpx auto 50rpx auto;
		border-radius: 20rpx;
	}

	.title {
		font-size: 36rpx;
		color: #333;
		text-align: center;
		margin-bottom: 80rpx;
		font-weight: bold;
	}

	.actions {
		width: 100%;
		display: flex;
		flex-direction: column;
		gap: 30rpx;
		margin-bottom: 60rpx;
	}

	.btn {
		width: 100%;
		height: 88rpx;
		background: #007AFF;
		color: white;
		border: none;
		border-radius: 44rpx;
		font-size: 32rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.btn:active {
		opacity: 0.8;
	}

	.user-info {
		width: 100%;
		padding: 40rpx;
		background: #f8f8f8;
		border-radius: 20rpx;
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}

	.info-text {
		font-size: 28rpx;
		color: #666;
	}
</style>
