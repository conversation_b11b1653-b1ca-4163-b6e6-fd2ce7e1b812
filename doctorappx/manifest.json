{
	"name": "doctorappx",
	"appid": "__UNI__FD8E18A",
	"description": "",
	"versionName": "1.0.0",
	"versionCode": "100",
	"uni-app-x": {},
	/* 快应用特有相关 */
	"quickapp": {},
	/* 小程序特有相关 */
	"mp-weixin": {
		"appid": "wx5083ba6d79dec9df",
		"setting": {
			"urlCheck": false,
			"es6": true,
			"postcss": true,
			"minified": true
		},
		"usingComponents": true,
		"permission": {
			"scope.userLocation": {
				"desc": "获取医护人员的位置信息,确保安全"
			}
		}
	},
	"mp-alipay": {
		"usingComponents": true
	},
	"mp-baidu": {
		"usingComponents": true
	},
	"mp-toutiao": {
		"usingComponents": true
	},
	"uniStatistics": {
		"enable": false
	},
	"vueVersion": "3",
	"app": {
		"distribute": {
			"icons": {
				"android": {
					"hdpi": "",
					"xhdpi": "",
					"xxhdpi": "",
					"xxxhdpi": ""
				}
			}
		}
	},
	"app-android": {
		"distribute": {
			"modules": {},
			"icons": {
				"hdpi": "",
				"xhdpi": "",
				"xxhdpi": "",
				"xxxhdpi": ""
			},
			"splashScreens": {
				"default": {}
			}
		}
	},
	"app-ios": {
		"distribute": {
			"modules": {},
			"icons": {},
			"splashScreens": {}
		}
	}
}