// Authentication utilities for Doctor App

export type UserInfo = {
  nickname: string
  avatarUrl: string
}

export type LoginResponse = {
  token: string
  user?: UserInfo
  is_admin: boolean
}

// Check if user is logged in
export function isUserLoggedIn(): boolean {
  const token = uni.getStorageSync('token') as string
  return token != ''
}

// Get current user info from storage
export function getCurrentUser(): UserInfo | null {
  const userInfo = uni.getStorageSync('userInfo') as UserInfo | null
  return userInfo
}

// Get current user token
export function getCurrentToken(): string {
  const token = uni.getStorageSync('token') as string
  return token
}

// Check if current user is admin
export function isCurrentUserAdmin(): boolean {
  const isAdmin = uni.getStorageSync('isAdmin') as boolean
  return isAdmin
}

// Clear all auth data
export function clearAuthData(): void {
  uni.removeStorageSync('token')
  uni.removeStorageSync('userInfo')
  uni.removeStorageSync('isAdmin')
}

// Save auth data to storage
export function saveAuthData(token: string, userInfo: UserInfo, isAdmin: boolean): void {
  uni.setStorageSync('token', token)
  uni.setStorageSync('userInfo', userInfo)
  uni.setStorageSync('isAdmin', isAdmin)
}
